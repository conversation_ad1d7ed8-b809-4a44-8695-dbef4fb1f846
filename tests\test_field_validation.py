"""
Test suite for field name validation against PostgreSQL reserved keywords
"""
import pytest
from erp.utils.validation.field_validator import (
    FieldNameValidator, FieldNameValidationError,
    validate_field_name, validate_model_fields
)
from erp.models import Model
from erp import fields


class TestFieldNameValidation:
    """Test field name validation functionality"""
    
    def test_valid_field_names(self):
        """Test that valid field names pass validation"""
        valid_names = [
            'name', 'description', 'active', 'sort_order', 
            'table_name_db', 'is_indexed', 'user_name'
        ]
        
        for field_name in valid_names:
            # Should not raise any exception
            validate_field_name(field_name)
    
    def test_postgresql_reserved_keywords(self):
        """Test that PostgreSQL reserved keywords are rejected"""
        reserved_keywords = [
            'table', 'index', 'sequence', 'order', 'group', 'user',
            'select', 'from', 'where', 'insert', 'update', 'delete'
        ]
        
        for keyword in reserved_keywords:
            with pytest.raises(FieldNameValidationError) as exc_info:
                validate_field_name(keyword)
            
            assert "PostgreSQL reserved keyword" in str(exc_info.value)
    
    def test_erp_reserved_keywords(self):
        """Test that ERP reserved keywords are rejected"""
        erp_keywords = ['id', 'create_at', 'update_at', 'write_date']
        
        for keyword in erp_keywords:
            with pytest.raises(FieldNameValidationError) as exc_info:
                validate_field_name(keyword)
            
            assert "reserved by the ERP system" in str(exc_info.value)
    
    def test_keyword_alternatives(self):
        """Test that alternatives are suggested for common keywords"""
        alternatives = FieldNameValidator.KEYWORD_ALTERNATIVES
        
        for keyword, expected_alternative in alternatives.items():
            with pytest.raises(FieldNameValidationError) as exc_info:
                validate_field_name(keyword)
            
            assert expected_alternative in str(exc_info.value)
    
    def test_invalid_python_identifiers(self):
        """Test that invalid Python identifiers are rejected"""
        invalid_names = ['123field', 'field-name', 'field name', 'field.name']
        
        for field_name in invalid_names:
            with pytest.raises(FieldNameValidationError) as exc_info:
                validate_field_name(field_name)
            
            assert "not a valid Python identifier" in str(exc_info.value)
    
    def test_double_underscore_validation(self):
        """Test validation of double underscore patterns"""
        # Valid double underscore patterns (Python special methods)
        valid_patterns = ['__init__', '__str__', '__repr__']
        for pattern in valid_patterns:
            validate_field_name(pattern)  # Should not raise
        
        # Invalid double underscore patterns
        invalid_patterns = ['field__name', 'test__field']
        for pattern in invalid_patterns:
            with pytest.raises(FieldNameValidationError) as exc_info:
                validate_field_name(pattern)
            
            assert "double underscores" in str(exc_info.value)
    
    def test_model_field_validation(self):
        """Test validation of all fields in a model"""
        # This should fail because 'table' is a reserved keyword
        with pytest.raises(FieldNameValidationError):
            class InvalidModel(Model):
                _name = 'test.invalid'
                
                table = fields.Char(string='Table Name')
    
    def test_valid_model_creation(self):
        """Test that models with valid field names can be created"""
        # This should succeed
        class ValidModel(Model):
            _name = 'test.valid'
            
            table_name_db = fields.Char(string='Table Name')
            is_indexed = fields.Boolean(string='Indexed')
            sort_order = fields.Integer(string='Sort Order')
        
        # Verify fields were created correctly
        assert 'table_name_db' in ValidModel._fields
        assert 'is_indexed' in ValidModel._fields
        assert 'sort_order' in ValidModel._fields
    
    def test_get_all_reserved_keywords(self):
        """Test getting all reserved keywords"""
        all_keywords = FieldNameValidator.get_all_reserved_keywords()
        
        # Should include PostgreSQL keywords
        assert 'table' in all_keywords
        assert 'index' in all_keywords
        assert 'sequence' in all_keywords
        
        # Should include ERP keywords
        assert 'id' in all_keywords
        assert 'create_at' in all_keywords
    
    def test_suggest_alternative(self):
        """Test alternative suggestion functionality"""
        # Test known alternatives
        assert FieldNameValidator.suggest_alternative('table') == 'table_name_db'
        assert FieldNameValidator.suggest_alternative('index') == 'is_indexed'
        assert FieldNameValidator.suggest_alternative('sequence') == 'sort_order'
        
        # Test unknown keyword
        assert FieldNameValidator.suggest_alternative('unknown_keyword') is None
    
    def test_validate_model_fields_function(self):
        """Test the validate_model_fields convenience function"""
        class TestModel(Model):
            _name = 'test.model'
            
            name = fields.Char(string='Name')
            description = fields.Text(string='Description')
        
        # Should return empty list (no errors)
        errors = validate_model_fields(TestModel)
        assert errors == []


if __name__ == '__main__':
    pytest.main([__file__])
