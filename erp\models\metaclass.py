"""
Model metaclass for handling field definitions and model setup
"""
from ..fields import Field
from ..utils.validation.field_validator import FieldNameValidator, FieldNameValidationError
from ..logging import get_logger

logger = get_logger(__name__)


class ModelMeta(type):
    """Metaclass for models to handle field definitions"""
    
    def __new__(cls, name, bases, attrs):
        # Collect fields from the class and its parents
        fields = {}
        
        # Get fields from parent classes
        for base in bases:
            if hasattr(base, '_fields'):
                fields.update(base._fields)
        
        # Get fields from current class and validate field names
        model_name = attrs.get('_name', name)
        for key, value in list(attrs.items()):
            if isinstance(value, Field):
                # Validate field name against PostgreSQL reserved keywords
                try:
                    FieldNameValidator.validate_field_name(key, model_name)
                except FieldNameValidationError as e:
                    logger.error(f"Field validation error in model {model_name}: {e}")
                    raise e

                fields[key] = value
                # Remove field from class attributes to avoid conflicts
                del attrs[key]

        # Store fields in the class
        attrs['_fields'] = fields

        # Auto-generate _table from _name if _table is None
        if attrs.get('_table') is None and attrs.get('_name'):
            attrs['_table'] = attrs['_name'].replace('.', '_')

        # Create the class
        new_class = super().__new__(cls, name, bases, attrs)

        return new_class
