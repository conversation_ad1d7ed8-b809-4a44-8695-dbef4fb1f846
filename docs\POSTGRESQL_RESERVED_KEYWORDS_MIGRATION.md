# PostgreSQL Reserved Keywords Migration

This document describes the migration from escaping PostgreSQL reserved keywords to renaming them in the ERP system.

## Overview

Previously, the ERP system used the `quote_identifier()` function to escape PostgreSQL reserved keywords by wrapping them in double quotes. This approach had several issues:

- Made SQL queries more complex and harder to read
- Caused compatibility issues with some PostgreSQL tools
- Made debugging more difficult
- Created inconsistent behavior across different database operations

The new approach validates field names during model definition and rejects PostgreSQL reserved keywords, requiring developers to use alternative names.

## Changes Made

### 1. Field Name Validation

A new field validation system has been implemented:

- **Location**: `erp/utils/validation/field_validator.py`
- **Functionality**: Validates field names against PostgreSQL reserved keywords during model definition
- **Integration**: Automatically called by the model metaclass when models are defined

### 2. Renamed Fields

The following fields in base models have been renamed:

| Model | Old Field Name | New Field Name | Description |
|-------|---------------|----------------|-------------|
| `ir.model` | `table` | `table_name_db` | Database table name |
| `ir.model.fields` | `index` | `is_indexed` | Whether field is indexed |
| `res.groups` | `sequence` | `sort_order` | Sort order for groups |
| `ir.module.module` | `sequence` | `sort_order` | Sort order for modules |
| `ir.module.category` | `sequence` | `sort_order` | Sort order for categories |

### 3. Schema Generation Updates

- Removed `quote_identifier()` usage for field names in schema generation
- Added deprecation warning to `quote_identifier()` function
- Updated all SQL generation to use unquoted field names

### 4. Migration Script

A migration script has been created to handle existing databases:

- **Location**: `erp/migrations/rename_reserved_keywords.py`
- **CLI Tool**: `erp/cli/migrate.py`
- **Functionality**: Renames columns in existing database tables

## Usage

### For New Installations

No action required. The field validation will automatically prevent use of reserved keywords.

### For Existing Installations

Run the migration script:

```bash
# Check what needs to be migrated (dry run)
python -m erp.cli.migrate your_database --action check

# Run the migration
python -m erp.cli.migrate your_database --action migrate

# Rollback if needed
python -m erp.cli.migrate your_database --action rollback
```

### Field Name Validation

When defining new models, the system will automatically validate field names:

```python
from erp.models import Model
from erp import fields

# This will raise FieldNameValidationError
class InvalidModel(Model):
    _name = 'my.model'
    
    table = fields.Char(string='Table')  # ERROR: 'table' is reserved

# This is correct
class ValidModel(Model):
    _name = 'my.model'
    
    table_name = fields.Char(string='Table Name')  # OK
```

## Recommended Alternatives

The field validator suggests alternatives for common reserved keywords:

| Reserved Keyword | Recommended Alternative |
|-----------------|------------------------|
| `table` | `table_name_db` |
| `index` | `is_indexed` |
| `sequence` | `sort_order` |
| `order` | `sort_order` |
| `group` | `group_name` |
| `user` | `user_name` |
| `column` | `column_name` |
| `key` | `key_name` |
| `default` | `default_value` |
| `check` | `check_constraint` |
| `constraint` | `constraint_name` |
| `references` | `reference_field` |
| `role` | `role_name` |
| `view` | `view_name` |
| `trigger` | `trigger_name` |
| `function` | `function_name` |
| `procedure` | `procedure_name` |

## Testing

Run the field validation tests:

```bash
python -m pytest tests/test_field_validation.py -v
```

Update existing tests that reference old field names:

```python
# Old
assert model.table == 'expected_table'
assert field.index == True
assert group.sequence == 10

# New
assert model.table_name_db == 'expected_table'
assert field.is_indexed == True
assert group.sort_order == 10
```

## Backward Compatibility

### Deprecated Functions

- `quote_identifier()` is deprecated for field names but still available for table names
- A deprecation warning is issued when used

### Database Migration

- The migration script handles existing databases automatically
- Column data is preserved during renaming
- Foreign key constraints are maintained

## Benefits

1. **Cleaner SQL**: No more quoted identifiers in generated SQL
2. **Better Compatibility**: Works with all PostgreSQL tools and clients
3. **Improved Debugging**: SQL queries are more readable
4. **Consistent Behavior**: No special handling needed for reserved keywords
5. **Future-Proof**: Prevents similar issues with new fields

## Troubleshooting

### Migration Issues

If migration fails:

1. Check database permissions
2. Ensure no active connections are using the tables
3. Review the migration logs for specific errors
4. Use the rollback option if needed

### Field Validation Errors

If you encounter field validation errors:

1. Check the error message for suggested alternatives
2. Use the recommended naming conventions
3. Avoid PostgreSQL reserved keywords
4. Follow Python identifier rules

### Legacy Code

For legacy code that references old field names:

1. Update all references to use new field names
2. Run tests to ensure compatibility
3. Update documentation and comments

## Support

For issues related to this migration:

1. Check the migration logs
2. Review the field validation error messages
3. Consult the recommended alternatives table
4. Test changes in a development environment first
