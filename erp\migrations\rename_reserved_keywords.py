"""
Migration script to rename PostgreSQL reserved keyword fields
This script renames fields that are PostgreSQL reserved keywords to avoid conflicts
"""
import asyncio
from typing import Dict, List, Tuple
from ..logging import get_logger
from ..database.manager import DatabaseManager

logger = get_logger(__name__)


class ReservedKeywordMigration:
    """
    Migration to rename PostgreSQL reserved keyword fields in base models
    """
    
    # Mapping of (table_name, old_column_name) -> new_column_name
    COLUMN_RENAMES = {
        ('ir_model', 'table'): 'table_name_db',
        ('ir_model_fields', 'index'): 'is_indexed',
        ('res_groups', 'sequence'): 'sort_order',
        ('ir_module_module', 'sequence'): 'sort_order',
        ('ir_module_category', 'sequence'): 'sort_order',
    }
    
    def __init__(self):
        self.logger = logger
    
    async def check_migration_needed(self, db_manager: DatabaseManager) -> bool:
        """
        Check if migration is needed by checking if old columns exist
        
        Args:
            db_manager: Database manager instance
            
        Returns:
            True if migration is needed, False otherwise
        """
        try:
            migration_needed = False
            
            for (table_name, old_column), new_column in self.COLUMN_RENAMES.items():
                # Check if table exists
                table_exists = await db_manager.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_name = $1
                    )
                """, table_name)
                
                if not table_exists:
                    self.logger.debug(f"Table {table_name} does not exist, skipping")
                    continue
                
                # Check if old column exists
                old_column_exists = await db_manager.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_name = $1 AND column_name = $2
                    )
                """, table_name, old_column)
                
                # Check if new column exists
                new_column_exists = await db_manager.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_name = $1 AND column_name = $2
                    )
                """, table_name, new_column)
                
                if old_column_exists and not new_column_exists:
                    self.logger.info(f"Migration needed: {table_name}.{old_column} -> {new_column}")
                    migration_needed = True
                elif old_column_exists and new_column_exists:
                    self.logger.warning(f"Both old and new columns exist in {table_name}: {old_column}, {new_column}")
                elif not old_column_exists and new_column_exists:
                    self.logger.debug(f"Migration already completed for {table_name}.{old_column}")
                else:
                    self.logger.debug(f"Neither column exists in {table_name}: {old_column}, {new_column}")
            
            return migration_needed
            
        except Exception as e:
            self.logger.error(f"Error checking migration status: {e}")
            return False
    
    async def run_migration(self, db_manager: DatabaseManager) -> bool:
        """
        Run the migration to rename reserved keyword columns
        
        Args:
            db_manager: Database manager instance
            
        Returns:
            True if migration successful, False otherwise
        """
        try:
            self.logger.info("Starting reserved keyword field migration...")
            
            # Check if migration is needed
            if not await self.check_migration_needed(db_manager):
                self.logger.info("No migration needed - all columns already renamed")
                return True
            
            # Perform migration for each table
            for (table_name, old_column), new_column in self.COLUMN_RENAMES.items():
                success = await self._migrate_table_column(db_manager, table_name, old_column, new_column)
                if not success:
                    self.logger.error(f"Failed to migrate {table_name}.{old_column}")
                    return False
            
            self.logger.info("Reserved keyword field migration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            return False
    
    async def _migrate_table_column(self, db_manager: DatabaseManager, table_name: str, 
                                   old_column: str, new_column: str) -> bool:
        """
        Migrate a single table column
        
        Args:
            db_manager: Database manager instance
            table_name: Name of the table
            old_column: Old column name
            new_column: New column name
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Check if table exists
            table_exists = await db_manager.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = $1
                )
            """, table_name)
            
            if not table_exists:
                self.logger.debug(f"Table {table_name} does not exist, skipping")
                return True
            
            # Check if old column exists
            old_column_exists = await db_manager.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = $1 AND column_name = $2
                )
            """, table_name, old_column)
            
            if not old_column_exists:
                self.logger.debug(f"Column {table_name}.{old_column} does not exist, skipping")
                return True
            
            # Check if new column already exists
            new_column_exists = await db_manager.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = $1 AND column_name = $2
                )
            """, table_name, new_column)
            
            if new_column_exists:
                self.logger.warning(f"New column {table_name}.{new_column} already exists")
                # If both exist, we need to decide what to do
                # For now, we'll skip and let manual intervention handle it
                return True
            
            # Rename the column
            self.logger.info(f"Renaming {table_name}.{old_column} to {new_column}")
            
            await db_manager.execute(f"""
                ALTER TABLE {table_name} 
                RENAME COLUMN "{old_column}" TO {new_column}
            """)
            
            self.logger.info(f"✓ Successfully renamed {table_name}.{old_column} to {new_column}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to migrate {table_name}.{old_column}: {e}")
            return False
    
    async def rollback_migration(self, db_manager: DatabaseManager) -> bool:
        """
        Rollback the migration by renaming columns back to original names
        
        Args:
            db_manager: Database manager instance
            
        Returns:
            True if rollback successful, False otherwise
        """
        try:
            self.logger.info("Starting migration rollback...")
            
            # Reverse the column renames
            for (table_name, old_column), new_column in self.COLUMN_RENAMES.items():
                success = await self._migrate_table_column(db_manager, table_name, new_column, old_column)
                if not success:
                    self.logger.error(f"Failed to rollback {table_name}.{new_column}")
                    return False
            
            self.logger.info("Migration rollback completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Rollback failed: {e}")
            return False


async def run_reserved_keyword_migration(db_manager: DatabaseManager) -> bool:
    """
    Convenience function to run the reserved keyword migration
    
    Args:
        db_manager: Database manager instance
        
    Returns:
        True if migration successful, False otherwise
    """
    migration = ReservedKeywordMigration()
    return await migration.run_migration(db_manager)


async def check_migration_needed(db_manager: DatabaseManager) -> bool:
    """
    Convenience function to check if migration is needed
    
    Args:
        db_manager: Database manager instance
        
    Returns:
        True if migration is needed, False otherwise
    """
    migration = ReservedKeywordMigration()
    return await migration.check_migration_needed(db_manager)


async def rollback_migration(db_manager: DatabaseManager) -> bool:
    """
    Convenience function to rollback the migration
    
    Args:
        db_manager: Database manager instance
        
    Returns:
        True if rollback successful, False otherwise
    """
    migration = ReservedKeywordMigration()
    return await migration.rollback_migration(db_manager)
