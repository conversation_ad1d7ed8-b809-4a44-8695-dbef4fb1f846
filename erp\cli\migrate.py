"""
Migration CLI commands for ERP system
"""
import asyncio
import sys
from typing import Optional
from ..database.manager import DatabaseManager
from ..migrations import run_reserved_keyword_migration, check_migration_needed, rollback_migration
from ..logging import get_logger

logger = get_logger(__name__)


async def migrate_reserved_keywords(database_name: str, dry_run: bool = False) -> bool:
    """
    Run the reserved keywords migration
    
    Args:
        database_name: Name of the database
        dry_run: If True, only check what would be migrated
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Initialize database manager
        db_manager = DatabaseManager()
        await db_manager.initialize(database_name)
        
        if dry_run:
            logger.info("Running migration check (dry run)...")
            needed = await check_migration_needed(db_manager)
            if needed:
                logger.info("Migration is needed - some reserved keyword fields need to be renamed")
            else:
                logger.info("No migration needed - all fields already renamed")
            return True
        else:
            logger.info("Running reserved keywords migration...")
            success = await run_reserved_keyword_migration(db_manager)
            if success:
                logger.info("Migration completed successfully")
            else:
                logger.error("Migration failed")
            return success
            
    except Exception as e:
        logger.error(f"Migration error: {e}")
        return False
    finally:
        if 'db_manager' in locals():
            await db_manager.close()


async def rollback_reserved_keywords(database_name: str) -> bool:
    """
    Rollback the reserved keywords migration
    
    Args:
        database_name: Name of the database
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Initialize database manager
        db_manager = DatabaseManager()
        await db_manager.initialize(database_name)
        
        logger.info("Rolling back reserved keywords migration...")
        success = await rollback_migration(db_manager)
        if success:
            logger.info("Rollback completed successfully")
        else:
            logger.error("Rollback failed")
        return success
        
    except Exception as e:
        logger.error(f"Rollback error: {e}")
        return False
    finally:
        if 'db_manager' in locals():
            await db_manager.close()


def main():
    """Main CLI entry point for migration commands"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ERP Database Migration Tool')
    parser.add_argument('database', help='Database name')
    parser.add_argument('--action', choices=['migrate', 'rollback', 'check'], 
                       default='migrate', help='Migration action to perform')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Check what would be migrated without making changes')
    
    args = parser.parse_args()
    
    if args.action == 'migrate':
        success = asyncio.run(migrate_reserved_keywords(args.database, args.dry_run))
    elif args.action == 'rollback':
        if args.dry_run:
            logger.error("Dry run not supported for rollback")
            sys.exit(1)
        success = asyncio.run(rollback_reserved_keywords(args.database))
    elif args.action == 'check':
        success = asyncio.run(migrate_reserved_keywords(args.database, dry_run=True))
    else:
        logger.error(f"Unknown action: {args.action}")
        sys.exit(1)
    
    if not success:
        sys.exit(1)


if __name__ == '__main__':
    main()
