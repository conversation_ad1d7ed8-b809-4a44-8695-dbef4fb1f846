"""
Field name validation utilities for ERP system
Validates field names against PostgreSQL reserved keywords and naming conventions
"""
from typing import Set, List, Optional
from ..schema.types import POSTGRESQL_RESERVED_KEYWORDS
from ...logging import get_logger

logger = get_logger(__name__)


class FieldNameValidationError(Exception):
    """Exception raised when field name validation fails"""
    pass


class FieldNameValidator:
    """
    Validator for field names to ensure they don't conflict with PostgreSQL reserved keywords
    and follow proper naming conventions
    """
    
    # Additional reserved words specific to ERP system
    ERP_RESERVED_KEYWORDS = {
        'id', 'create_at', 'update_at', 'write_date', 'create_date',
        'write_uid', 'create_uid', '__last_update'
    }
    
    # Recommended alternatives for common reserved keywords
    KEYWORD_ALTERNATIVES = {
        'table': 'table_name_db',
        'index': 'is_indexed',
        'sequence': 'sort_order',
        'order': 'sort_order',
        'group': 'group_name',
        'user': 'user_name',
        'column': 'column_name',
        'key': 'key_name',
        'default': 'default_value',
        'check': 'check_constraint',
        'constraint': 'constraint_name',
        'references': 'reference_field',
        'role': 'role_name',
        'view': 'view_name',
        'trigger': 'trigger_name',
        'function': 'function_name',
        'procedure': 'procedure_name',
    }
    
    @classmethod
    def validate_field_name(cls, field_name: str, model_name: str = None) -> None:
        """
        Validate a field name against PostgreSQL reserved keywords and naming conventions
        
        Args:
            field_name: The field name to validate
            model_name: Optional model name for better error messages
            
        Raises:
            FieldNameValidationError: If field name is invalid
        """
        if not field_name:
            raise FieldNameValidationError("Field name cannot be empty")
        
        # Check if field name is a PostgreSQL reserved keyword
        if field_name.lower() in POSTGRESQL_RESERVED_KEYWORDS:
            alternative = cls.KEYWORD_ALTERNATIVES.get(field_name.lower())
            model_info = f" in model '{model_name}'" if model_name else ""
            
            if alternative:
                raise FieldNameValidationError(
                    f"Field name '{field_name}'{model_info} is a PostgreSQL reserved keyword. "
                    f"Consider using '{alternative}' instead."
                )
            else:
                raise FieldNameValidationError(
                    f"Field name '{field_name}'{model_info} is a PostgreSQL reserved keyword. "
                    f"Please choose a different name."
                )
        
        # Check if field name is an ERP reserved keyword
        if field_name.lower() in cls.ERP_RESERVED_KEYWORDS:
            model_info = f" in model '{model_name}'" if model_name else ""
            raise FieldNameValidationError(
                f"Field name '{field_name}'{model_info} is reserved by the ERP system. "
                f"Please choose a different name."
            )
        
        # Check naming conventions
        cls._validate_naming_conventions(field_name, model_name)
    
    @classmethod
    def _validate_naming_conventions(cls, field_name: str, model_name: str = None) -> None:
        """
        Validate field name against naming conventions
        
        Args:
            field_name: The field name to validate
            model_name: Optional model name for better error messages
        """
        model_info = f" in model '{model_name}'" if model_name else ""
        
        # Check if field name is a valid Python identifier
        if not field_name.isidentifier():
            raise FieldNameValidationError(
                f"Field name '{field_name}'{model_info} is not a valid Python identifier"
            )
        
        # Check if field name starts with underscore (reserved for system fields)
        if field_name.startswith('_') and not field_name.startswith('__'):
            logger.warning(
                f"Field name '{field_name}'{model_info} starts with underscore. "
                f"This is typically reserved for system fields."
            )
        
        # Check for double underscores (reserved for Python special methods)
        if '__' in field_name and not (field_name.startswith('__') and field_name.endswith('__')):
            raise FieldNameValidationError(
                f"Field name '{field_name}'{model_info} contains double underscores. "
                f"This pattern is reserved for Python special methods."
            )
        
        # Recommend snake_case
        if field_name != field_name.lower() or '-' in field_name:
            logger.warning(
                f"Field name '{field_name}'{model_info} should use snake_case convention"
            )
    
    @classmethod
    def get_all_reserved_keywords(cls) -> Set[str]:
        """
        Get all reserved keywords (PostgreSQL + ERP specific)
        
        Returns:
            Set of all reserved keywords
        """
        return POSTGRESQL_RESERVED_KEYWORDS | cls.ERP_RESERVED_KEYWORDS
    
    @classmethod
    def suggest_alternative(cls, field_name: str) -> Optional[str]:
        """
        Suggest an alternative name for a reserved keyword
        
        Args:
            field_name: The field name to get alternative for
            
        Returns:
            Suggested alternative name or None if no suggestion available
        """
        return cls.KEYWORD_ALTERNATIVES.get(field_name.lower())
    
    @classmethod
    def validate_model_fields(cls, model_class: type) -> List[str]:
        """
        Validate all field names in a model class
        
        Args:
            model_class: The model class to validate
            
        Returns:
            List of validation errors (empty if all valid)
        """
        errors = []
        model_name = getattr(model_class, '_name', model_class.__name__)
        
        if hasattr(model_class, '_fields'):
            for field_name in model_class._fields.keys():
                try:
                    cls.validate_field_name(field_name, model_name)
                except FieldNameValidationError as e:
                    errors.append(str(e))
        
        return errors


def validate_field_name(field_name: str, model_name: str = None) -> None:
    """
    Convenience function to validate a field name
    
    Args:
        field_name: The field name to validate
        model_name: Optional model name for better error messages
        
    Raises:
        FieldNameValidationError: If field name is invalid
    """
    FieldNameValidator.validate_field_name(field_name, model_name)


def validate_model_fields(model_class: type) -> List[str]:
    """
    Convenience function to validate all field names in a model
    
    Args:
        model_class: The model class to validate
        
    Returns:
        List of validation errors (empty if all valid)
    """
    return FieldNameValidator.validate_model_fields(model_class)
